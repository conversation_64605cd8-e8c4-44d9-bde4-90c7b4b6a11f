"""
Import Analysis Visualization Module

This module provides visualization capabilities for import analysis results,
creating interactive and static visualizations that help developers understand:

1. Import dependency graphs
2. Circular dependency cycles
3. Module coupling relationships
4. Import complexity heatmaps
5. Optimization opportunities

These visualizations are key differentiators for Vibe Check as a specialized
Python analysis platform.
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import networkx as nx
from dataclasses import asdict

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as mpatches
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    HAS_PLOTLY = True
except ImportError:
    HAS_PLOTLY = False

from .import_analyzer import ImportAnalysisResult, CircularDependency


class ImportVisualizer:
    """Visualizer for import analysis results."""
    
    def __init__(self, analysis_result: ImportAnalysisResult, output_dir: str):
        """Initialize the visualizer.
        
        Args:
            analysis_result: Results from import analysis
            output_dir: Directory to save visualizations
        """
        self.analysis_result = analysis_result
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_all_visualizations(self) -> Dict[str, str]:
        """Generate all available visualizations.
        
        Returns:
            Dictionary mapping visualization names to file paths
        """
        visualizations = {}
        
        # Generate dependency graph
        dep_graph_path = self.generate_dependency_graph()
        if dep_graph_path:
            visualizations['dependency_graph'] = dep_graph_path
        
        # Generate circular dependency visualization
        circular_deps_path = self.generate_circular_dependencies_chart()
        if circular_deps_path:
            visualizations['circular_dependencies'] = circular_deps_path
        
        # Generate coupling heatmap
        coupling_heatmap_path = self.generate_coupling_heatmap()
        if coupling_heatmap_path:
            visualizations['coupling_heatmap'] = coupling_heatmap_path
        
        # Generate complexity chart
        complexity_chart_path = self.generate_complexity_chart()
        if complexity_chart_path:
            visualizations['complexity_chart'] = complexity_chart_path
        
        # Generate interactive dashboard
        dashboard_path = self.generate_interactive_dashboard()
        if dashboard_path:
            visualizations['interactive_dashboard'] = dashboard_path
        
        return visualizations
    
    def generate_dependency_graph(self) -> Optional[str]:
        """Generate a dependency graph visualization.
        
        Returns:
            Path to the generated visualization file
        """
        if not HAS_MATPLOTLIB:
            return None
        
        try:
            plt.figure(figsize=(16, 12))
            
            # Use spring layout for better visualization
            pos = nx.spring_layout(self.analysis_result.dependency_graph, k=3, iterations=50)
            
            # Draw nodes
            nx.draw_networkx_nodes(
                self.analysis_result.dependency_graph, 
                pos, 
                node_color='lightblue',
                node_size=1000,
                alpha=0.7
            )
            
            # Draw edges
            nx.draw_networkx_edges(
                self.analysis_result.dependency_graph,
                pos,
                edge_color='gray',
                arrows=True,
                arrowsize=20,
                alpha=0.6
            )
            
            # Draw labels (shortened for readability)
            labels = {node: os.path.basename(node) for node in self.analysis_result.dependency_graph.nodes()}
            nx.draw_networkx_labels(
                self.analysis_result.dependency_graph,
                pos,
                labels,
                font_size=8
            )
            
            plt.title("Import Dependency Graph", fontsize=16, fontweight='bold')
            plt.axis('off')
            plt.tight_layout()
            
            output_path = self.output_dir / "dependency_graph.png"
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(output_path)
            
        except Exception as e:
            print(f"Error generating dependency graph: {e}")
            return None
    
    def generate_circular_dependencies_chart(self) -> Optional[str]:
        """Generate a visualization of circular dependencies.
        
        Returns:
            Path to the generated visualization file
        """
        if not HAS_MATPLOTLIB or not self.analysis_result.circular_dependencies:
            return None
        
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            # Chart 1: Circular dependencies by severity
            severities = [cd.severity for cd in self.analysis_result.circular_dependencies]
            severity_counts = {sev: severities.count(sev) for sev in set(severities)}
            
            colors = {'high': 'red', 'medium': 'orange', 'low': 'yellow'}
            ax1.pie(
                severity_counts.values(),
                labels=severity_counts.keys(),
                colors=[colors.get(sev, 'gray') for sev in severity_counts.keys()],
                autopct='%1.1f%%',
                startangle=90
            )
            ax1.set_title("Circular Dependencies by Severity")
            
            # Chart 2: Cycle sizes
            cycle_sizes = [len(cd.cycle) for cd in self.analysis_result.circular_dependencies]
            ax2.hist(cycle_sizes, bins=range(2, max(cycle_sizes) + 2), alpha=0.7, color='skyblue')
            ax2.set_xlabel("Cycle Size (Number of Modules)")
            ax2.set_ylabel("Number of Cycles")
            ax2.set_title("Distribution of Circular Dependency Sizes")
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            output_path = self.output_dir / "circular_dependencies.png"
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(output_path)
            
        except Exception as e:
            print(f"Error generating circular dependencies chart: {e}")
            return None
    
    def generate_coupling_heatmap(self) -> Optional[str]:
        """Generate a coupling metrics heatmap.
        
        Returns:
            Path to the generated visualization file
        """
        if not HAS_MATPLOTLIB:
            return None
        
        try:
            coupling_data = self.analysis_result.coupling_metrics
            if not coupling_data:
                return None
            
            # Prepare data for heatmap
            files = list(coupling_data.keys())
            metrics = ['afferent_coupling', 'efferent_coupling', 'instability']
            
            data_matrix = []
            for metric in metrics:
                row = [coupling_data[file][metric] for file in files]
                data_matrix.append(row)
            
            fig, ax = plt.subplots(figsize=(max(12, len(files) * 0.5), 6))
            
            im = ax.imshow(data_matrix, cmap='YlOrRd', aspect='auto')
            
            # Set ticks and labels
            ax.set_xticks(range(len(files)))
            ax.set_xticklabels([os.path.basename(f) for f in files], rotation=45, ha='right')
            ax.set_yticks(range(len(metrics)))
            ax.set_yticklabels(metrics)
            
            # Add colorbar
            plt.colorbar(im, ax=ax)
            
            # Add text annotations
            for i in range(len(metrics)):
                for j in range(len(files)):
                    text = ax.text(j, i, f'{data_matrix[i][j]:.2f}',
                                 ha="center", va="center", color="black", fontsize=8)
            
            ax.set_title("Module Coupling Metrics Heatmap")
            plt.tight_layout()
            
            output_path = self.output_dir / "coupling_heatmap.png"
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(output_path)
            
        except Exception as e:
            print(f"Error generating coupling heatmap: {e}")
            return None
    
    def generate_complexity_chart(self) -> Optional[str]:
        """Generate an import complexity chart.
        
        Returns:
            Path to the generated visualization file
        """
        if not HAS_MATPLOTLIB:
            return None
        
        try:
            complexity_data = self.analysis_result.import_complexity_scores
            if not complexity_data:
                return None
            
            files = list(complexity_data.keys())
            scores = list(complexity_data.values())
            
            plt.figure(figsize=(max(12, len(files) * 0.3), 8))
            
            # Create bar chart
            bars = plt.bar(range(len(files)), scores, color='steelblue', alpha=0.7)
            
            # Color bars based on complexity level
            for i, (bar, score) in enumerate(zip(bars, scores)):
                if score > 20:
                    bar.set_color('red')
                elif score > 10:
                    bar.set_color('orange')
                else:
                    bar.set_color('green')
            
            plt.xlabel("Files")
            plt.ylabel("Import Complexity Score")
            plt.title("Import Complexity by File")
            plt.xticks(range(len(files)), [os.path.basename(f) for f in files], 
                      rotation=45, ha='right')
            plt.grid(True, alpha=0.3)
            
            # Add legend
            red_patch = mpatches.Patch(color='red', label='High Complexity (>20)')
            orange_patch = mpatches.Patch(color='orange', label='Medium Complexity (10-20)')
            green_patch = mpatches.Patch(color='green', label='Low Complexity (<10)')
            plt.legend(handles=[red_patch, orange_patch, green_patch])
            
            plt.tight_layout()
            
            output_path = self.output_dir / "import_complexity.png"
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(output_path)
            
        except Exception as e:
            print(f"Error generating complexity chart: {e}")
            return None

    def _create_static_html_dashboard(self) -> str:
        """Create a static HTML dashboard with analysis results.

        Returns:
            HTML content as string
        """
        html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1, h2 { color: #333; }
        .metric-card { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        .success { border-left-color: #28a745; }
        .file-list { max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; }
        .cycle { background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .complexity-high { color: #dc3545; font-weight: bold; }
        .complexity-medium { color: #ffc107; font-weight: bold; }
        .complexity-low { color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Import Analysis Report</h1>

        <div class="metric-card">
            <h2>Summary</h2>
            <p><strong>Total Files Analyzed:</strong> {total_files}</p>
            <p><strong>Total Import Statements:</strong> {total_imports}</p>
            <p><strong>Circular Dependencies Found:</strong> {circular_deps_count}</p>
            <p><strong>Files with Unused Imports:</strong> {unused_imports_count}</p>
        </div>

        {circular_deps_section}

        {unused_imports_section}

        {optimization_section}

        {complexity_section}

        {coupling_section}
    </div>
</body>
</html>
        """

        # Calculate summary statistics
        total_files = len(self.analysis_result.file_imports)
        total_imports = sum(len(imports) for imports in self.analysis_result.file_imports.values())
        circular_deps_count = len(self.analysis_result.circular_dependencies)
        unused_imports_count = len(self.analysis_result.unused_imports)

        # Generate sections
        circular_deps_section = self._generate_circular_deps_html()
        unused_imports_section = self._generate_unused_imports_html()
        optimization_section = self._generate_optimization_html()
        complexity_section = self._generate_complexity_html()
        coupling_section = self._generate_coupling_html()

        return html.format(
            total_files=total_files,
            total_imports=total_imports,
            circular_deps_count=circular_deps_count,
            unused_imports_count=unused_imports_count,
            circular_deps_section=circular_deps_section,
            unused_imports_section=unused_imports_section,
            optimization_section=optimization_section,
            complexity_section=complexity_section,
            coupling_section=coupling_section
        )

    def _generate_circular_deps_html(self) -> str:
        """Generate HTML for circular dependencies section."""
        if not self.analysis_result.circular_dependencies:
            return '<div class="metric-card success"><h2>Circular Dependencies</h2><p>No circular dependencies found! 🎉</p></div>'

        html = '<div class="metric-card error"><h2>Circular Dependencies</h2>'

        for i, cd in enumerate(self.analysis_result.circular_dependencies):
            severity_class = f"cycle-{cd.severity}"
            html += f'''
            <div class="cycle">
                <h3>Cycle {i+1} (Severity: {cd.severity.upper()})</h3>
                <p><strong>Modules:</strong> {" → ".join([os.path.basename(f) for f in cd.cycle])}</p>
                <p><strong>Description:</strong> {cd.description}</p>
                <p><strong>Suggestions:</strong></p>
                <ul>
                    {"".join(f"<li>{suggestion}</li>" for suggestion in cd.suggestions)}
                </ul>
            </div>
            '''

        html += '</div>'
        return html

    def _generate_unused_imports_html(self) -> str:
        """Generate HTML for unused imports section."""
        if not self.analysis_result.unused_imports:
            return '<div class="metric-card success"><h2>Unused Imports</h2><p>No unused imports detected! 🎉</p></div>'

        html = '<div class="metric-card warning"><h2>Unused Imports</h2>'

        for file_path, unused in self.analysis_result.unused_imports.items():
            html += f'<h3>{os.path.basename(file_path)}</h3><ul>'
            for import_info in unused:
                if import_info.from_module:
                    import_text = f"from {import_info.from_module} import {import_info.module}"
                else:
                    import_text = f"import {import_info.module}"
                html += f'<li>Line {import_info.line_number}: {import_text}</li>'
            html += '</ul>'

        html += '</div>'
        return html

    def _generate_optimization_html(self) -> str:
        """Generate HTML for optimization suggestions section."""
        if not self.analysis_result.optimization_suggestions:
            return '<div class="metric-card success"><h2>Optimization Suggestions</h2><p>No optimization suggestions at this time.</p></div>'

        html = '<div class="metric-card"><h2>Optimization Suggestions</h2>'

        for file_path, suggestions in self.analysis_result.optimization_suggestions.items():
            html += f'<h3>{os.path.basename(file_path)}</h3><ul>'
            for suggestion in suggestions:
                html += f'<li>{suggestion}</li>'
            html += '</ul>'

        html += '</div>'
        return html

    def _generate_complexity_html(self) -> str:
        """Generate HTML for complexity metrics section."""
        if not self.analysis_result.import_complexity_scores:
            return '<div class="metric-card"><h2>Import Complexity</h2><p>No complexity data available.</p></div>'

        html = '<div class="metric-card"><h2>Import Complexity</h2>'
        html += '<table><tr><th>File</th><th>Complexity Score</th><th>Level</th></tr>'

        for file_path, score in sorted(self.analysis_result.import_complexity_scores.items(),
                                     key=lambda x: x[1], reverse=True):
            if score > 20:
                level = "High"
                css_class = "complexity-high"
            elif score > 10:
                level = "Medium"
                css_class = "complexity-medium"
            else:
                level = "Low"
                css_class = "complexity-low"

            html += f'<tr><td>{os.path.basename(file_path)}</td><td class="{css_class}">{score:.1f}</td><td class="{css_class}">{level}</td></tr>'

        html += '</table></div>'
        return html

    def _generate_coupling_html(self) -> str:
        """Generate HTML for coupling metrics section."""
        if not self.analysis_result.coupling_metrics:
            return '<div class="metric-card"><h2>Coupling Metrics</h2><p>No coupling data available.</p></div>'

        html = '<div class="metric-card"><h2>Coupling Metrics</h2>'
        html += '<table><tr><th>File</th><th>Afferent</th><th>Efferent</th><th>Instability</th><th>Total Imports</th></tr>'

        for file_path, metrics in self.analysis_result.coupling_metrics.items():
            html += f'''<tr>
                <td>{os.path.basename(file_path)}</td>
                <td>{metrics["afferent_coupling"]}</td>
                <td>{metrics["efferent_coupling"]}</td>
                <td>{metrics["instability"]:.2f}</td>
                <td>{metrics["total_imports"]}</td>
            </tr>'''

        html += '</table></div>'
        return html
    
    def generate_interactive_dashboard(self) -> Optional[str]:
        """Generate an interactive HTML dashboard.
        
        Returns:
            Path to the generated HTML file
        """
        if not HAS_PLOTLY:
            return self._generate_static_dashboard()
        
        try:
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Dependency Graph', 'Circular Dependencies', 
                              'Coupling Metrics', 'Import Complexity'),
                specs=[[{"type": "scatter"}, {"type": "bar"}],
                       [{"type": "heatmap"}, {"type": "bar"}]]
            )
            
            # Add dependency graph (simplified)
            if self.analysis_result.dependency_graph.nodes():
                pos = nx.spring_layout(self.analysis_result.dependency_graph)
                edge_x, edge_y = [], []
                for edge in self.analysis_result.dependency_graph.edges():
                    x0, y0 = pos[edge[0]]
                    x1, y1 = pos[edge[1]]
                    edge_x.extend([x0, x1, None])
                    edge_y.extend([y0, y1, None])
                
                fig.add_trace(
                    go.Scatter(x=edge_x, y=edge_y, mode='lines', 
                             line=dict(width=1, color='gray'), showlegend=False),
                    row=1, col=1
                )
                
                node_x = [pos[node][0] for node in self.analysis_result.dependency_graph.nodes()]
                node_y = [pos[node][1] for node in self.analysis_result.dependency_graph.nodes()]
                node_text = [os.path.basename(node) for node in self.analysis_result.dependency_graph.nodes()]
                
                fig.add_trace(
                    go.Scatter(x=node_x, y=node_y, mode='markers+text', 
                             text=node_text, textposition="middle center",
                             marker=dict(size=10, color='lightblue'), showlegend=False),
                    row=1, col=1
                )
            
            # Add other charts...
            # (Additional Plotly charts would be added here)
            
            fig.update_layout(height=800, title_text="Import Analysis Dashboard")
            
            output_path = self.output_dir / "import_dashboard.html"
            fig.write_html(str(output_path))
            
            return str(output_path)
            
        except Exception as e:
            print(f"Error generating interactive dashboard: {e}")
            return self._generate_static_dashboard()
    
    def _generate_static_dashboard(self) -> Optional[str]:
        """Generate a static HTML dashboard as fallback.
        
        Returns:
            Path to the generated HTML file
        """
        try:
            html_content = self._create_static_html_dashboard()
            
            output_path = self.output_dir / "import_analysis_report.html"
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return str(output_path)
            
        except Exception as e:
            print(f"Error generating static dashboard: {e}")
            return None
