"""
Adaptive Configuration Module

This module provides a simplified, lightweight approach to adaptive behavior
that replaces the complex CAW (Contextual Adaptive Wave) system. It uses
simple configuration profiles and conditional logic to provide context-aware
analysis behavior.

This is part of the strategic transformation to make Vibe Check a specialized
Python analysis platform rather than a complex tool aggregator.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import json


@dataclass
class AnalysisProfile:
    """Configuration profile for adaptive analysis behavior."""
    name: str
    description: str
    
    # Analysis focus areas
    focus_security: bool = False
    focus_performance: bool = False
    focus_maintainability: bool = False
    focus_imports: bool = True  # Always focus on imports for Python analysis
    
    # Tool configurations
    enabled_tools: List[str] = field(default_factory=lambda: [
        "ruff", "mypy", "bandit", "complexity", "custom_rules"
    ])
    
    # Import analysis settings
    import_analysis_enabled: bool = True
    detect_circular_imports: bool = True
    detect_unused_imports: bool = True
    import_complexity_threshold: float = 15.0
    
    # Visualization settings
    generate_visualizations: bool = True
    visualization_types: List[str] = field(default_factory=lambda: [
        "dependency_graph", "import_analysis", "complexity_charts"
    ])
    
    # Performance settings
    max_files_for_detailed_analysis: int = 1000
    enable_parallel_processing: bool = True
    
    # Output settings
    generate_reports: bool = True
    report_formats: List[str] = field(default_factory=lambda: ["html", "json"])


class AdaptiveConfigManager:
    """Manages adaptive configuration profiles and context-aware behavior."""
    
    def __init__(self):
        """Initialize the adaptive config manager."""
        self.profiles: Dict[str, AnalysisProfile] = {}
        self._load_default_profiles()
    
    def _load_default_profiles(self) -> None:
        """Load default analysis profiles."""
        
        # Minimal profile for quick analysis
        self.profiles["minimal"] = AnalysisProfile(
            name="minimal",
            description="Quick analysis with essential checks only",
            enabled_tools=["ruff", "custom_rules"],
            import_analysis_enabled=True,
            generate_visualizations=False,
            max_files_for_detailed_analysis=500
        )
        
        # Standard profile for regular development
        self.profiles["standard"] = AnalysisProfile(
            name="standard",
            description="Balanced analysis for regular development",
            enabled_tools=["ruff", "mypy", "bandit", "complexity", "custom_rules"],
            import_analysis_enabled=True,
            generate_visualizations=True,
            visualization_types=["dependency_graph", "import_analysis"]
        )
        
        # Comprehensive profile for thorough analysis
        self.profiles["comprehensive"] = AnalysisProfile(
            name="comprehensive",
            description="Thorough analysis with all available tools",
            enabled_tools=["ruff", "mypy", "bandit", "complexity", "custom_rules"],
            import_analysis_enabled=True,
            detect_circular_imports=True,
            detect_unused_imports=True,
            generate_visualizations=True,
            visualization_types=["dependency_graph", "import_analysis", "complexity_charts", "coupling_heatmap"]
        )
        
        # Security-focused profile
        self.profiles["security"] = AnalysisProfile(
            name="security",
            description="Security-focused analysis",
            focus_security=True,
            enabled_tools=["bandit", "ruff", "custom_rules"],
            import_analysis_enabled=True,
            generate_visualizations=True,
            visualization_types=["dependency_graph", "import_analysis"]
        )
        
        # Performance-focused profile
        self.profiles["performance"] = AnalysisProfile(
            name="performance",
            description="Performance-focused analysis",
            focus_performance=True,
            enabled_tools=["complexity", "ruff", "custom_rules"],
            import_analysis_enabled=True,
            import_complexity_threshold=10.0,  # Lower threshold for performance focus
            generate_visualizations=True,
            visualization_types=["complexity_charts", "import_analysis"]
        )
        
        # Maintainability-focused profile
        self.profiles["maintainability"] = AnalysisProfile(
            name="maintainability",
            description="Maintainability-focused analysis",
            focus_maintainability=True,
            enabled_tools=["mypy", "ruff", "complexity", "custom_rules"],
            import_analysis_enabled=True,
            detect_circular_imports=True,
            detect_unused_imports=True,
            generate_visualizations=True,
            visualization_types=["dependency_graph", "import_analysis", "coupling_heatmap"]
        )
    
    def get_profile(self, profile_name: str) -> Optional[AnalysisProfile]:
        """Get an analysis profile by name.
        
        Args:
            profile_name: Name of the profile to retrieve
            
        Returns:
            AnalysisProfile if found, None otherwise
        """
        return self.profiles.get(profile_name)
    
    def list_profiles(self) -> List[str]:
        """List all available profile names.
        
        Returns:
            List of profile names
        """
        return list(self.profiles.keys())
    
    def create_adaptive_config(self, 
                             profile_name: str = "standard",
                             project_path: Optional[str] = None,
                             overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create an adaptive configuration based on profile and context.
        
        Args:
            profile_name: Name of the base profile to use
            project_path: Path to the project being analyzed
            overrides: Optional configuration overrides
            
        Returns:
            Configuration dictionary for analysis
        """
        # Get base profile
        profile = self.get_profile(profile_name)
        if not profile:
            profile = self.profiles["standard"]  # Fallback to standard
        
        # Start with profile configuration
        config = {
            "profile": profile.name,
            "tools": {
                "enabled": profile.enabled_tools.copy(),
                "ruff": {"enabled": "ruff" in profile.enabled_tools},
                "mypy": {"enabled": "mypy" in profile.enabled_tools},
                "bandit": {"enabled": "bandit" in profile.enabled_tools},
                "complexity": {"enabled": "complexity" in profile.enabled_tools},
                "custom_rules": {"enabled": "custom_rules" in profile.enabled_tools}
            },
            "import_analysis": {
                "enabled": profile.import_analysis_enabled,
                "detect_circular": profile.detect_circular_imports,
                "detect_unused": profile.detect_unused_imports,
                "complexity_threshold": profile.import_complexity_threshold
            },
            "visualization": {
                "enabled": profile.generate_visualizations,
                "types": profile.visualization_types.copy()
            },
            "performance": {
                "max_files": profile.max_files_for_detailed_analysis,
                "parallel": profile.enable_parallel_processing
            },
            "output": {
                "generate_reports": profile.generate_reports,
                "formats": profile.report_formats.copy()
            }
        }
        
        # Apply context-aware adaptations
        if project_path:
            config = self._apply_context_adaptations(config, project_path)
        
        # Apply overrides
        if overrides:
            config = self._apply_overrides(config, overrides)
        
        return config
    
    def _apply_context_adaptations(self, config: Dict[str, Any], project_path: str) -> Dict[str, Any]:
        """Apply context-aware adaptations based on project characteristics.
        
        Args:
            config: Base configuration
            project_path: Path to the project
            
        Returns:
            Adapted configuration
        """
        project_dir = Path(project_path)
        
        # Adapt based on project size
        python_files = list(project_dir.rglob("*.py"))
        file_count = len(python_files)
        
        if file_count > 1000:
            # Large project - optimize for performance
            config["performance"]["parallel"] = True
            config["performance"]["max_files"] = min(config["performance"]["max_files"], 800)
            config["visualization"]["enabled"] = False  # Skip heavy visualizations
        elif file_count < 50:
            # Small project - enable all features
            config["visualization"]["enabled"] = True
            config["import_analysis"]["enabled"] = True
        
        # Adapt based on project structure
        has_tests = any(project_dir.rglob("test*.py")) or any(project_dir.rglob("*test*.py"))
        if has_tests:
            # Project has tests - focus on maintainability
            if "mypy" not in config["tools"]["enabled"]:
                config["tools"]["enabled"].append("mypy")
                config["tools"]["mypy"]["enabled"] = True
        
        # Check for security-sensitive patterns
        has_web_framework = any(
            any(keyword in file.read_text(encoding='utf-8', errors='ignore').lower() 
                for keyword in ['flask', 'django', 'fastapi', 'tornado'])
            for file in python_files[:10]  # Check first 10 files only
        )
        if has_web_framework:
            # Web framework detected - enable security analysis
            if "bandit" not in config["tools"]["enabled"]:
                config["tools"]["enabled"].append("bandit")
                config["tools"]["bandit"]["enabled"] = True
        
        return config
    
    def _apply_overrides(self, config: Dict[str, Any], overrides: Dict[str, Any]) -> Dict[str, Any]:
        """Apply configuration overrides.
        
        Args:
            config: Base configuration
            overrides: Override values
            
        Returns:
            Configuration with overrides applied
        """
        def deep_update(base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> Dict[str, Any]:
            """Recursively update nested dictionaries."""
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
            return base_dict
        
        return deep_update(config, overrides)
    
    def save_profile(self, profile: AnalysisProfile, file_path: str) -> None:
        """Save a profile to a file.
        
        Args:
            profile: Profile to save
            file_path: Path to save the profile
        """
        profile_data = {
            "name": profile.name,
            "description": profile.description,
            "focus_security": profile.focus_security,
            "focus_performance": profile.focus_performance,
            "focus_maintainability": profile.focus_maintainability,
            "focus_imports": profile.focus_imports,
            "enabled_tools": profile.enabled_tools,
            "import_analysis_enabled": profile.import_analysis_enabled,
            "detect_circular_imports": profile.detect_circular_imports,
            "detect_unused_imports": profile.detect_unused_imports,
            "import_complexity_threshold": profile.import_complexity_threshold,
            "generate_visualizations": profile.generate_visualizations,
            "visualization_types": profile.visualization_types,
            "max_files_for_detailed_analysis": profile.max_files_for_detailed_analysis,
            "enable_parallel_processing": profile.enable_parallel_processing,
            "generate_reports": profile.generate_reports,
            "report_formats": profile.report_formats
        }
        
        with open(file_path, 'w') as f:
            json.dump(profile_data, f, indent=2)
    
    def load_profile(self, file_path: str) -> Optional[AnalysisProfile]:
        """Load a profile from a file.
        
        Args:
            file_path: Path to load the profile from
            
        Returns:
            Loaded AnalysisProfile or None if loading failed
        """
        try:
            with open(file_path, 'r') as f:
                profile_data = json.load(f)
            
            profile = AnalysisProfile(**profile_data)
            self.profiles[profile.name] = profile
            return profile
            
        except (FileNotFoundError, json.JSONDecodeError, TypeError) as e:
            print(f"Error loading profile from {file_path}: {e}")
            return None


# Global instance for easy access
adaptive_config_manager = AdaptiveConfigManager()
