"""
CLI Main Module
===========

This module provides the main CLI interface for Vibe Check.
"""

import logging
import os
import sys
from typing import Any, Dict, Optional, cast

import click

from ..core.version import __version__
from .commands import analyze_command, tui_command, web_command, plugin_command, debug_command
from .error_handler import handle_analysis_error

# Configure root logger
logger = logging.getLogger("vibe_check_cli")


@click.group()
@click.version_option(version=__version__, prog_name="Vibe Check")
def cli() -> None:
    """Vibe Check - A project analysis tool for Python codebases."""
    pass


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
@click.option("--output", "-o", type=click.Path(), help="Output directory")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose output")
@click.option("--quiet", "-q", is_flag=True, help="Suppress output")
@click.option("--security-focused", is_flag=True, help="Focus on security issues")
@click.option("--performance-focused", is_flag=True, help="Focus on performance issues")
@click.option("--maintainability-focused", is_flag=True, help="Focus on maintainability issues")
@click.option("--preset", "-p", type=click.Choice(["default", "minimal", "enhanced", "quick", "comprehensive", "quality", "security"]),
              help="Use a predefined configuration preset")
@click.option("--profile", type=click.Choice(["minimal", "standard", "comprehensive", "security", "performance", "maintainability"]),
              default="standard", help="Analysis profile to use (replaces complex CAW system)")
@click.option("--analyze-trends", is_flag=True, help="Analyze trends compared to previous runs")
@click.option("--report-progress", is_flag=True, help="Report progress between analyses with the same output directory")
@click.option("--custom-report", is_flag=True, help="Generate a custom report")
@click.option("--debug", is_flag=True, help="Enable debug mode with enhanced logging")
@click.option("--log-file", type=click.Path(), help="Path to save detailed logs")
def analyze(project_path: str, config: Optional[str] = None, output: Optional[str] = None,
            verbose: bool = False, quiet: bool = False, security_focused: bool = False,
            performance_focused: bool = False, maintainability_focused: bool = False,
            preset: Optional[str] = None, profile: str = "standard", analyze_trends: bool = False,
            report_progress: bool = False, custom_report: bool = False,
            debug: bool = False, log_file: Optional[str] = None) -> None:
    """Analyze a Python project."""
    import logging
    logger = logging.getLogger("vibe_check_cli.analyze")

    try:
        # Log the command parameters
        logger.debug(f"Analyzing project: {project_path}")
        logger.debug(f"Config: {config}, Output: {output}")
        logger.debug(f"Verbose: {verbose}, Quiet: {quiet}")
        logger.debug(f"Security focused: {security_focused}, Performance focused: {performance_focused}, Maintainability focused: {maintainability_focused}")
        logger.debug(f"Preset: {preset}, Profile: {profile}")
        logger.debug(f"Analyze trends: {analyze_trends}, Report progress: {report_progress}")
        logger.debug(f"Custom report: {custom_report}")

        # Determine profile based on focus flags or explicit profile
        if security_focused:
            profile = "security"
        elif performance_focused:
            profile = "performance"
        elif maintainability_focused:
            profile = "maintainability"
        # Otherwise use the explicitly provided profile

        # Create context with priorities (legacy support)
        context: Dict[str, Any] = {"priorities": {}}
        if profile == "security":
            context["priorities"]["security"] = 0.8
            context["priorities"]["performance"] = 0.1
            context["priorities"]["maintainability"] = 0.1
        elif profile == "performance":
            context["priorities"]["security"] = 0.1
            context["priorities"]["performance"] = 0.8
            context["priorities"]["maintainability"] = 0.1
        elif profile == "maintainability":
            context["priorities"]["security"] = 0.1
            context["priorities"]["performance"] = 0.1
            context["priorities"]["maintainability"] = 0.8
        else:
            # Default priorities
            context["priorities"]["security"] = 0.33
            context["priorities"]["performance"] = 0.33
            context["priorities"]["maintainability"] = 0.34

        # Set up config overrides
        config_override: Dict[str, Any] = {}

        # Apply preset if specified
        if preset:
            config_override["preset"] = preset
            logger.debug(f"Using preset: {preset}")

        # Enable custom report if requested
        if custom_report:
            config_override["reporting"] = {
                "generate_custom_report": True
            }
            logger.debug("Custom report enabled")

        # Show a message to the user
        if not quiet:
            click.echo(f"Analyzing project: {project_path}")
            click.echo(f"Using analysis profile: {profile}")
            if preset:
                click.echo(f"Using preset: {preset}")
            if output:
                click.echo(f"Output directory: {output}")
            if debug:
                click.echo("Debug mode enabled")
            if log_file:
                click.echo(f"Logging to file: {log_file}")
            click.echo("Analysis in progress...")

        # Run the analysis
        results = analyze_command(
            project_path=project_path,
            config_path=config,
            output_dir=output,
            verbose=verbose,
            quiet=quiet,
            config_override=config_override,
            analyze_trends=analyze_trends,
            report_progress=report_progress,
            profile=profile
        )

        # Check if there was an error
        if isinstance(results, dict) and "error" in results:
            # Handle the error using our error handler
            from .error_handler import handle_analysis_error
            handle_analysis_error(results)
            # This will exit with code 1, so we won't reach here

        # Format and display the results
        try:
            formatted_results = format_analysis_results(results)
            click.echo(formatted_results)
        except Exception as format_error:
            logger.error(f"Error formatting results: {format_error}")
            # Fallback to simple output
            if hasattr(results, 'total_file_count'):
                click.echo(f"Analysis completed successfully!")
                click.echo(f"Files analyzed: {results.total_file_count}")
                click.echo(f"Issues found: {getattr(results, 'issue_count', 'N/A')}")
            else:
                click.echo("Analysis completed successfully!")
                click.echo(f"Results: {results}")

    except Exception as e:
        import traceback
        logger.error(f"Error during analysis: {e}")
        logger.error(traceback.format_exc())
        click.echo(f"Error during analysis: {e}")
        click.echo("For more details, run with --verbose flag")
        sys.exit(1)


@cli.command()
def profiles() -> None:
    """List available analysis profiles."""
    from ..core.analysis.adaptive_config import adaptive_config_manager

    click.echo("Available Analysis Profiles:")
    click.echo("=" * 30)

    for profile_name in adaptive_config_manager.list_profiles():
        profile = adaptive_config_manager.get_profile(profile_name)
        if profile:
            click.echo(f"\n{profile.name}:")
            click.echo(f"  Description: {profile.description}")
            click.echo(f"  Tools: {', '.join(profile.enabled_tools)}")
            click.echo(f"  Import Analysis: {'Yes' if profile.import_analysis_enabled else 'No'}")
            click.echo(f"  Visualizations: {'Yes' if profile.generate_visualizations else 'No'}")


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
def tui(project_path: str, config: Optional[str] = None) -> None:
    """Launch the terminal user interface."""
    tui_command(project_path, config)


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
@click.option("--host", "-h", default="localhost", help="Host to bind to")
@click.option("--port", "-p", default=8000, help="Port to bind to")
def web(project_path: str, config: Optional[str] = None, host: str = "localhost", port: int = 8000) -> None:
    """Launch the web interface."""
    web_command(project_path, config, host, port)


@cli.group()
def plugin() -> None:
    """Manage plugins."""
    pass


@plugin.command("list")
def plugin_list() -> None:
    """List installed plugins."""
    plugin_command("list")


@plugin.command("install")
@click.argument("plugin_name")
def plugin_install(plugin_name: str) -> None:
    """Install a plugin."""
    plugin_command("install", plugin_name)


@plugin.command("uninstall")
@click.argument("plugin_name")
def plugin_uninstall(plugin_name: str) -> None:
    """Uninstall a plugin."""
    plugin_command("uninstall", plugin_name)


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--output", "-o", type=click.Path(), help="Output directory for debug information")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose output")
@click.option("--timeout", "-t", type=float, default=120.0, help="Maximum time in seconds to wait for initialization")
@click.option("--open-timeline", is_flag=True, help="Open the timeline and dependency graph visualizations in a browser")
@click.option("--dependency-analysis", is_flag=True, help="Focus on dependency resolution analysis")
@click.option("--registry-analysis", is_flag=True, help="Focus on registry synchronization analysis")
def debug(project_path: str, output: Optional[str] = None, verbose: bool = False, timeout: float = 120.0, open_timeline: bool = False, dependency_analysis: bool = False, registry_analysis: bool = False) -> None:
    """
    Debug the actor system initialization.

    This command enables comprehensive debugging for the actor system,
    including detailed logging, diagnostic tracing, and error trapping.
    It helps diagnose and resolve issues with the actor system initialization.
    """
    import logging
    logger = logging.getLogger("vibe_check_cli.debug")

    try:
        # Show a message to the user
        click.echo(f"Debugging actor system for project: {project_path}")
        click.echo(f"Timeout: {timeout} seconds")
        if output:
            click.echo(f"Output directory: {output}")
        if verbose:
            click.echo("Verbose mode enabled")
        if dependency_analysis:
            click.echo("Focus: Dependency Resolution Analysis")
        if registry_analysis:
            click.echo("Focus: Registry Synchronization Analysis")
        click.echo("Debugging in progress...")

        # Run the debug command
        results = debug_command(
            project_path=project_path,
            output_dir=output,
            verbose=verbose,
            timeout=timeout
        )

        # Check if there was an error
        if isinstance(results, dict) and "error" in results:
            click.echo(f"Error during debugging: {results['error']}")
            if "error_details" in results:
                if verbose:
                    click.echo(f"Error details: {results['error_details']}")
                else:
                    click.echo("Run with --verbose for detailed error information")

            # Check if a report was generated despite the error
            if "report_path" in results:
                click.echo(f"Debug report generated: {results['report_path']}")

            sys.exit(1)

        # Display results
        click.echo("\n=== Debug Results ===")

        # Show success status
        if results.get("success", False):
            click.echo("Actor system initialization debugging completed successfully")
        else:
            click.echo("Actor system initialization debugging completed with issues")

        # If registry analysis is requested, focus on registry synchronization
        if registry_analysis and "registry_sync_analysis" in results:
            # Skip other analysis sections and focus on registry synchronization
            registry_sync_analysis = results["registry_sync_analysis"]

            # Show registry synchronization issues
            if isinstance(registry_sync_analysis, dict) and "sync_issues" in registry_sync_analysis:
                sync_issues = registry_sync_analysis.get("sync_issues", [])
                if sync_issues:
                    click.echo("\n=== Registry Synchronization Issues ===")
                    for issue in sync_issues:
                        click.echo(f"- Issue Type: {issue.get('issue_type', 'unknown')}")
                        click.echo(f"  Message: {issue.get('message', '')}")

                        if "actors" in issue:
                            actors = issue.get("actors", [])
                            click.echo(f"  Actors: {', '.join(actors)}")

                        if "actor_id" in issue:
                            click.echo(f"  Actor ID: {issue.get('actor_id', '')}")

                        if "manager_state" in issue:
                            click.echo(f"  Manager State: {issue.get('manager_state', '')}")

                        click.echo("")
                else:
                    click.echo("\n=== Registry Synchronization ===")
                    click.echo("No synchronization issues found.")

            # Show consistency check results
            if isinstance(registry_sync_analysis, dict) and "consistency" in registry_sync_analysis:
                consistency = registry_sync_analysis.get("consistency", {})
                if isinstance(consistency, dict):
                    click.echo("\n=== Registry Consistency ===")
                    click.echo(f"Consistency Check: {'Passed' if consistency.get('consistent', False) else 'Failed'}")

                    # Show orphaned references
                    orphaned_refs = consistency.get("orphaned_references", {})
                    if orphaned_refs:
                        click.echo("\nOrphaned References:")
                        for ref_type, refs in orphaned_refs.items():
                            if refs:
                                click.echo(f"- {ref_type}: {len(refs)}")

                    # Show missing references
                    missing_refs = consistency.get("missing_references", {})
                    if missing_refs:
                        click.echo("\nMissing References:")
                        for ref_type, refs in missing_refs.items():
                            if refs:
                                click.echo(f"- {ref_type}: {len(refs)}")

            # Show registry visualization paths
            if "registry_viz_path" in results:
                registry_viz_path = results["registry_viz_path"]
                click.echo(f"\nRegistry visualization: {registry_viz_path}")

                # Open the registry visualization in a browser if requested
                if open_timeline:
                    click.echo("Opening registry visualization in browser...")
                    try:
                        import webbrowser
                        webbrowser.open(f"file://{os.path.abspath(registry_viz_path)}")
                    except Exception as e:
                        click.echo(f"Error opening browser: {e}")
                        click.echo("Please open the HTML file manually.")

            if "registry_sync_viz_path" in results:
                registry_sync_viz_path = results["registry_sync_viz_path"]
                click.echo(f"\nRegistry synchronization visualization: {registry_sync_viz_path}")

                # Open the registry synchronization visualization in a browser if requested
                if open_timeline:
                    click.echo("Opening registry synchronization visualization in browser...")
                    try:
                        import webbrowser
                        webbrowser.open(f"file://{os.path.abspath(registry_sync_viz_path)}")
                    except Exception as e:
                        click.echo(f"Error opening browser: {e}")
                        click.echo("Please open the HTML file manually.")

            # Show report path if available
            if "report_path" in results:
                click.echo(f"\nDetailed debug report: {results['report_path']}")

            # Skip other analysis sections
            return

        # If dependency analysis is requested, focus on dependency resolution
        elif dependency_analysis and "dependency_analysis" in results:
            # This section is already implemented
            pass

        # Show duration if available
        if "duration" in results:
            click.echo(f"Duration: {results['duration']:.2f} seconds")

        # Show deadlocks if any
        if "deadlocks" in results and results["deadlocks"]:
            click.echo("\n=== Deadlocks Detected ===")
            for i, cycle in enumerate(results["deadlocks"]):
                click.echo(f"Cycle {i+1}: {' -> '.join(cycle)}")

        # Show issues if any
        if "issues" in results:
            issues = results["issues"]

            # Count total issues
            total_issues = sum(len(issue_list) for issue_list in issues.values() if isinstance(issue_list, list))

            if total_issues > 0:
                click.echo(f"\n=== Issues Detected ({total_issues} total) ===")

                # Show timeout issues
                if issues.get("timeout_issues"):
                    click.echo(f"\nTimeout Issues ({len(issues['timeout_issues'])})")
                    for issue in issues["timeout_issues"]:
                        click.echo(f"- Actor: {issue.get('actor_id', 'Unknown')}")
                        click.echo(f"  Message: {issue.get('message', 'No message')}")

                # Show dependency issues
                if issues.get("dependency_issues"):
                    click.echo(f"\nDependency Issues ({len(issues['dependency_issues'])})")
                    for issue in issues["dependency_issues"]:
                        click.echo(f"- Actor: {issue.get('actor_id', 'Unknown')}")
                        click.echo(f"  Error: {issue.get('error', 'No error message')}")

                # Show state transition issues
                if issues.get("state_transition_issues"):
                    click.echo(f"\nState Transition Issues ({len(issues['state_transition_issues'])})")
                    for issue in issues["state_transition_issues"]:
                        click.echo(f"- Actor: {issue.get('actor_id', 'Unknown')}")
                        click.echo(f"  Error: {issue.get('error', 'No error message')}")

                # Show registration issues
                if issues.get("registration_issues"):
                    click.echo(f"\nRegistration Issues ({len(issues['registration_issues'])})")
                    for issue in issues["registration_issues"]:
                        click.echo(f"- Actor: {issue.get('actor_id', 'Unknown')}")
                        click.echo(f"  Message: {issue.get('message', 'No message')}")

                # Show initialization errors
                if issues.get("initialization_errors"):
                    click.echo(f"\nInitialization Errors ({len(issues['initialization_errors'])})")
                    for issue in issues["initialization_errors"]:
                        click.echo(f"- Actor: {issue.get('actor_id', 'Unknown')}")
                        click.echo(f"  Error: {issue.get('error', 'No error message')}")
            else:
                click.echo("\n=== No Issues Detected ===")

        # Show suggestions if any
        if "suggestions" in results:
            suggestions = results["suggestions"]

            # Count total suggestions
            total_suggestions = sum(len(suggestion_list) for suggestion_list in suggestions.values() if isinstance(suggestion_list, list))

            if total_suggestions > 0:
                click.echo(f"\n=== Suggestions ({total_suggestions} total) ===")

                # Show timeout suggestions
                if suggestions.get("timeout_issues"):
                    click.echo("\nFor Timeout Issues:")
                    for suggestion in suggestions["timeout_issues"]:
                        click.echo(f"- {suggestion}")

                # Show dependency suggestions
                if suggestions.get("dependency_issues"):
                    click.echo("\nFor Dependency Issues:")
                    for suggestion in suggestions["dependency_issues"]:
                        click.echo(f"- {suggestion}")

                # Show state transition suggestions
                if suggestions.get("state_transition_issues"):
                    click.echo("\nFor State Transition Issues:")
                    for suggestion in suggestions["state_transition_issues"]:
                        click.echo(f"- {suggestion}")

                # Show registration suggestions
                if suggestions.get("registration_issues"):
                    click.echo("\nFor Registration Issues:")
                    for suggestion in suggestions["registration_issues"]:
                        click.echo(f"- {suggestion}")

                # Show initialization error suggestions
                if suggestions.get("initialization_errors"):
                    click.echo("\nFor Initialization Errors:")
                    for suggestion in suggestions["initialization_errors"]:
                        click.echo(f"- {suggestion}")

        # Show timeout analysis if available
        if "timeout_analysis" in results:
            timeout_analysis = results["timeout_analysis"]

            # Show timeout recommendations
            if "timeout_recommendations" in timeout_analysis and timeout_analysis["timeout_recommendations"]:
                click.echo("\n=== Timeout Recommendations ===")
                for rec in timeout_analysis["timeout_recommendations"]:
                    if "step" in rec:
                        click.echo(f"- Step: {rec['step']}")
                    if "actor_id" in rec:
                        click.echo(f"  Actor: {rec['actor_id']}")
                    if "recommended_timeout" in rec:
                        click.echo(f"  Recommended timeout: {rec['recommended_timeout']:.2f}s")
                    if "recommended_approach" in rec:
                        click.echo(f"  Recommended approach: {rec['recommended_approach']}")
                    if "reason" in rec:
                        click.echo(f"  Reason: {rec['reason']}")
                    click.echo("")

            # Show slow actors
            if "slow_actors" in timeout_analysis and timeout_analysis["slow_actors"]:
                click.echo("\n=== Slow Actors ===")
                for step, actors in timeout_analysis["slow_actors"].items():
                    click.echo(f"\nStep: {step}")
                    for actor_data in actors:
                        click.echo(f"- Actor: {actor_data['actor_id']}")
                        click.echo(f"  Duration: {actor_data['duration']:.2f}s")
                        click.echo(f"  Avg Duration: {actor_data['avg_duration']:.2f}s")
                        click.echo(f"  Ratio: {actor_data['ratio']:.1f}x slower than average")

        # Show dependency analysis if available
        if "dependency_analysis" in results:
            dependency_analysis = results["dependency_analysis"]

            # Show dependency cycles
            try:
                # Cast to avoid type checking issues
                dep_analysis_dict = cast(Dict[str, Any], dependency_analysis)

                if "graph" in dep_analysis_dict:
                    graph = dep_analysis_dict.get("graph", {})
                    if "cycles" in graph:
                        cycles = graph.get("cycles", [])
                        if cycles:
                            click.echo("\n=== Dependency Cycles Detected ===")
                            for i, cycle in enumerate(cycles):
                                click.echo(f"Cycle {i+1}: {' -> '.join(cycle)}")
                            click.echo("\nCycles in dependency relationships can cause deadlocks and initialization failures.")
                            click.echo("Consider restructuring your actor dependencies to eliminate cycles.")
            except Exception as e:
                click.echo(f"Error displaying dependency cycles: {e}")

            # Show problematic dependencies
            try:
                # Cast to avoid type checking issues
                dep_analysis_dict = cast(Dict[str, Any], dependency_analysis)

                if "analysis" in dep_analysis_dict:
                    analysis = dep_analysis_dict.get("analysis", {})
                    if "problematic_dependencies" in analysis:
                        problematic_deps = analysis.get("problematic_dependencies", [])
                        if problematic_deps:
                            click.echo("\n=== Problematic Dependencies ===")
                            for dep in problematic_deps:
                                if isinstance(dep, dict):
                                    click.echo(f"- Actor: {dep.get('actor_id', 'unknown')}")
                                    click.echo(f"  Depends on: {dep.get('dependency_id', 'unknown')}")
                                    click.echo(f"  Optional: {'Yes' if dep.get('optional', False) else 'No'}")
                                    click.echo(f"  Failed: {'Yes' if dep.get('failed', False) else 'No'}")
                                    click.echo(f"  In Cycle: {'Yes' if dep.get('cycle_detected', False) else 'No'}")
                                    click.echo("")
            except Exception as e:
                click.echo(f"Error displaying problematic dependencies: {e}")

        # Show registry synchronization analysis if available
        if "registry_sync_analysis" in results:
            registry_sync_analysis = results["registry_sync_analysis"]

            # Show synchronization issues
            if isinstance(registry_sync_analysis, dict) and "sync_issues" in registry_sync_analysis:
                sync_issues = registry_sync_analysis.get("sync_issues", [])
                if sync_issues:
                    click.echo("\n=== Registry Synchronization Issues ===")
                    for issue in sync_issues:
                        click.echo(f"- Issue Type: {issue.get('issue_type', 'unknown')}")
                        click.echo(f"  Message: {issue.get('message', '')}")

                        if "actors" in issue:
                            actors = issue.get("actors", [])
                            click.echo(f"  Actors: {', '.join(actors)}")

                        if "actor_id" in issue:
                            click.echo(f"  Actor ID: {issue.get('actor_id', '')}")

                        if "manager_state" in issue:
                            click.echo(f"  Manager State: {issue.get('manager_state', '')}")

                        click.echo("")
                else:
                    click.echo("\n=== Registry Synchronization ===")
                    click.echo("No synchronization issues found.")

            # Show consistency check results
            if isinstance(registry_sync_analysis, dict) and "consistency" in registry_sync_analysis:
                consistency = registry_sync_analysis.get("consistency", {})
                if isinstance(consistency, dict):
                    click.echo("\n=== Registry Consistency ===")
                    click.echo(f"Consistency Check: {'Passed' if consistency.get('consistent', False) else 'Failed'}")

                    # Show orphaned references
                    orphaned_refs = consistency.get("orphaned_references", {})
                    if orphaned_refs:
                        click.echo("\nOrphaned References:")
                        for ref_type, refs in orphaned_refs.items():
                            if refs:
                                click.echo(f"- {ref_type}: {len(refs)}")

                    # Show missing references
                    missing_refs = consistency.get("missing_references", {})
                    if missing_refs:
                        click.echo("\nMissing References:")
                        for ref_type, refs in missing_refs.items():
                            if refs:
                                click.echo(f"- {ref_type}: {len(refs)}")

        # Show dependency graph path if available
        if "dependency_graph_path" in results:
            dependency_graph_path = results["dependency_graph_path"]
            click.echo(f"\nDependency graph visualization: {dependency_graph_path}")

            # Open the dependency graph visualization in a browser if requested
            if open_timeline:
                click.echo("Opening dependency graph visualization in browser...")
                try:
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(dependency_graph_path)}")
                except Exception as e:
                    click.echo(f"Error opening browser: {e}")
                    click.echo("Please open the HTML file manually.")

        # Show registry visualization path if available
        if "registry_viz_path" in results:
            registry_viz_path = results["registry_viz_path"]
            click.echo(f"\nRegistry visualization: {registry_viz_path}")

            # Open the registry visualization in a browser if requested
            if open_timeline:
                click.echo("Opening registry visualization in browser...")
                try:
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(registry_viz_path)}")
                except Exception as e:
                    click.echo(f"Error opening browser: {e}")
                    click.echo("Please open the HTML file manually.")

        # Show registry synchronization visualization path if available
        if "registry_sync_viz_path" in results:
            registry_sync_viz_path = results["registry_sync_viz_path"]
            click.echo(f"\nRegistry synchronization visualization: {registry_sync_viz_path}")

            # Open the registry synchronization visualization in a browser if requested
            if open_timeline:
                click.echo("Opening registry synchronization visualization in browser...")
                try:
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(registry_sync_viz_path)}")
                except Exception as e:
                    click.echo(f"Error opening browser: {e}")
                    click.echo("Please open the HTML file manually.")

        # Show timeline path if available
        if "timeline_path" in results:
            timeline_path = results["timeline_path"]
            click.echo(f"\nInitialization timeline visualization: {timeline_path}")

            # Open the timeline visualization in a browser if requested
            if open_timeline:
                click.echo("Opening timeline visualization in browser...")
                try:
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(timeline_path)}")
                except Exception as e:
                    click.echo(f"Error opening browser: {e}")
                    click.echo("Please open the HTML file manually.")
            else:
                click.echo("Use --open-timeline to automatically open the visualizations in a browser.")

        # Show report path if available
        if "report_path" in results:
            click.echo(f"\nDetailed debug report: {results['report_path']}")

    except Exception as e:
        import traceback
        logger.error(f"Error during debugging: {e}")
        logger.error(traceback.format_exc())
        click.echo(f"Error during debugging: {e}")
        click.echo("For more details, run with --verbose flag")
        sys.exit(1)


def format_analysis_results(results: Any) -> str:
    """
    Format analysis results for display.

    Args:
        results: Analysis results

    Returns:
        Formatted results string
    """
    # Set up logger
    logger = logging.getLogger("vibe_check_cli.formatter")
    output = []

    # Check if results is a dictionary with an error
    if isinstance(results, dict) and "error" in results:
        # Use the error handler to format the error
        from .error_handler import format_error_results
        return format_error_results(results)

    # Check if results is empty or None
    if results is None or (isinstance(results, dict) and not results):
        output.append("=== Analysis Error ===")
        output.append("Error: No results were returned from the analysis")
        output.append("\nSuggestions:")
        output.append("- Try using the --use-simple-analyzer flag to bypass the actor system")
        output.append("- Run with --debug or --debug-actor-system for more detailed logs")
        output.append("- Check the log file for more details")
        return "\n".join(output)

    # Add summary
    output.append("=== Analysis Summary ===")

    # Handle different result types
    try:
        # Check if results has total_file_count attribute
        if hasattr(results, 'total_file_count'):
            output.append(f"Total Files: {results.total_file_count}")
        # Check if results is a dictionary with total_file_count
        elif isinstance(results, dict) and "total_file_count" in results:
            output.append(f"Total Files: {results['total_file_count']}")
        # Check if results has files attribute
        elif hasattr(results, 'files'):
            output.append(f"Total Files: {len(results.files)}")
        # Check if results is a dictionary with files
        elif isinstance(results, dict) and "files" in results:
            output.append(f"Total Files: {len(results['files'])}")
        else:
            output.append("Total Files: Not available")
    except Exception as e:
        logger.error(f"Error formatting total_file_count: {e}")
        output.append("Total Files: Not available")

    try:
        # Check if results has total_line_count attribute
        if hasattr(results, 'total_line_count'):
            output.append(f"Total Lines: {results.total_line_count}")
        # Check if results is a dictionary with total_line_count
        elif isinstance(results, dict) and "total_line_count" in results:
            output.append(f"Total Lines: {results['total_line_count']}")
        # Try to calculate from files
        elif hasattr(results, 'files'):
            try:
                total_lines = sum(getattr(file_metrics, 'lines', 0) for file_metrics in results.files.values())
                output.append(f"Total Lines: {total_lines}")
            except Exception as e:
                logger.error(f"Error calculating total lines from files: {e}")
                output.append("Total Lines: Not available")
        # Try to calculate from files in dictionary
        elif isinstance(results, dict) and "files" in results:
            try:
                total_lines = 0
                for file_metrics in results['files'].values():
                    if isinstance(file_metrics, dict):
                        total_lines += file_metrics.get('lines', 0)
                    else:
                        total_lines += getattr(file_metrics, 'lines', 0)
                output.append(f"Total Lines: {total_lines}")
            except Exception as e:
                logger.error(f"Error calculating total lines from files dict: {e}")
                output.append("Total Lines: Not available")
        else:
            output.append("Total Lines: Not available")
    except Exception as e:
        logger.error(f"Error formatting total_line_count: {e}")
        output.append("Total Lines: Not available")

    try:
        # Check if results has avg_complexity attribute
        if hasattr(results, 'avg_complexity'):
            output.append(f"Average Complexity: {results.avg_complexity:.2f}")
        # Check if results is a dictionary with avg_complexity
        elif isinstance(results, dict) and "avg_complexity" in results:
            output.append(f"Average Complexity: {results['avg_complexity']:.2f}")
        else:
            output.append("Average Complexity: Not available")
    except Exception as e:
        logger.error(f"Error formatting avg_complexity: {e}")
        output.append("Average Complexity: Not available")

    try:
        # Check if results has issue_count attribute
        if hasattr(results, 'issue_count'):
            output.append(f"Total Issues: {results.issue_count}")
        # Check if results is a dictionary with issue_count
        elif isinstance(results, dict) and "issue_count" in results:
            output.append(f"Total Issues: {results['issue_count']}")
        # Try to calculate from files
        elif hasattr(results, 'files'):
            try:
                total_issues = 0
                for file_metrics in results.files.values():
                    issues = getattr(file_metrics, 'issues', [])
                    if issues is not None:
                        total_issues += len(issues)
                output.append(f"Total Issues: {total_issues}")
            except Exception as e:
                logger.error(f"Error calculating total issues from files: {e}")
                output.append("Total Issues: Not available")
        # Try to calculate from files in dictionary
        elif isinstance(results, dict) and "files" in results:
            try:
                total_issues = 0
                for file_metrics in results['files'].values():
                    if isinstance(file_metrics, dict):
                        issues = file_metrics.get('issues', [])
                        if issues is not None:
                            total_issues += len(issues)
                    else:
                        issues = getattr(file_metrics, 'issues', [])
                        if issues is not None:
                            total_issues += len(issues)
                output.append(f"Total Issues: {total_issues}")
            except Exception as e:
                logger.error(f"Error calculating total issues from files dict: {e}")
                output.append("Total Issues: Not available")
        else:
            output.append("Total Issues: Not available")
    except Exception as e:
        logger.error(f"Error formatting issue_count: {e}")
        output.append("Total Issues: Not available")

    # Add type and docstring coverage if available
    if hasattr(results, 'avg_type_coverage'):
        output.append(f"Type Coverage: {results.avg_type_coverage:.2f}%")
    elif isinstance(results, dict) and "avg_type_coverage" in results:
        output.append(f"Type Coverage: {results['avg_type_coverage']:.2f}%")

    if hasattr(results, 'avg_doc_coverage'):
        output.append(f"Docstring Coverage: {results.avg_doc_coverage:.2f}%")
    elif isinstance(results, dict) and "avg_doc_coverage" in results:
        output.append(f"Docstring Coverage: {results['avg_doc_coverage']:.2f}%")

    # Add issue summary if available
    issues_by_severity = None
    if hasattr(results, 'issues_by_severity'):
        issues_by_severity = results.issues_by_severity
    elif isinstance(results, dict) and "issues_by_severity" in results:
        issues_by_severity = results["issues_by_severity"]

    if issues_by_severity:
        output.append("\n=== Issue Summary ===")
        for severity, count in issues_by_severity.items():
            output.append(f"{severity}: {count}")

    # Add trend summary if available
    trend_results = None
    if hasattr(results, 'trend_results'):
        trend_results = results.trend_results
    elif isinstance(results, dict) and "trend_results" in results:
        trend_results = results["trend_results"]

    if trend_results:
        output.append("\n=== Trend Analysis ===")
        if isinstance(trend_results, dict) and trend_results.get('has_historical_data', False):
            output.append(trend_results.get('summary', 'No trend summary available.'))
        else:
            output.append(getattr(trend_results, 'message', 'No historical data available for trend analysis.'))

    # Add progress summary if available
    progress_results = None
    if hasattr(results, 'progress_results'):
        progress_results = results.progress_results
    elif isinstance(results, dict) and "progress_results" in results:
        progress_results = results["progress_results"]

    if progress_results:
        output.append("\n=== Progress Analysis ===")
        if isinstance(progress_results, dict) and progress_results.get('has_progress_data', False):
            progress_data = progress_results.get('progress', {})
            output.append(progress_data.get('summary', 'No progress summary available.'))
            output.append(f"Output Directory: {progress_data.get('output_dir', 'Unknown')}")
            output.append(f"Run Count: {progress_data.get('run_count', 0)}")
            output.append(f"First Run: {progress_data.get('first_run_date', 'Unknown')}")
        else:
            message = progress_results.get('message', 'Not enough data to analyze progress.') if isinstance(progress_results, dict) else 'Not enough data to analyze progress.'
            output.append(message)

    # Add report paths if available
    generated_reports = []

    trend_visualizations = None
    if hasattr(results, 'trend_visualizations'):
        trend_visualizations = results.trend_visualizations
    elif isinstance(results, dict) and "trend_visualizations" in results:
        trend_visualizations = results["trend_visualizations"]

    if trend_visualizations:
        if isinstance(trend_visualizations, dict):
            generated_reports.extend([f"Trend Report - {report_name}: {report_path}"
                                    for report_name, report_path in trend_visualizations.items()])
        else:
            generated_reports.append(f"Trend Reports available: {trend_visualizations}")

    progress_visualizations = None
    if hasattr(results, 'progress_visualizations'):
        progress_visualizations = results.progress_visualizations
    elif isinstance(results, dict) and "progress_visualizations" in results:
        progress_visualizations = results["progress_visualizations"]

    if progress_visualizations:
        if isinstance(progress_visualizations, dict):
            generated_reports.extend([f"Progress Report - {report_name}: {report_path}"
                                    for report_name, report_path in progress_visualizations.items()])
        else:
            generated_reports.append(f"Progress Reports available: {progress_visualizations}")

    if generated_reports:
        output.append("\n=== Generated Reports ===")
        output.extend(generated_reports)

    return "\n".join(output)


def main() -> None:
    """Entry point for the CLI."""
    try:
        import logging
        import sys
        import traceback

        # Configure logging
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        logger = logging.getLogger("vibe_check_cli")

        logger.debug("Starting Vibe Check CLI")
        logger.debug(f"Python version: {sys.version}")
        logger.debug(f"Arguments: {sys.argv}")

        # Run the CLI
        cli()

        logger.debug("Vibe Check CLI completed successfully")
    except Exception as e:
        import traceback
        print(f"Error in Vibe Check CLI: {e}")
        print(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
